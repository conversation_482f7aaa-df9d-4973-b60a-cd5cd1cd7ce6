import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:audio_service/audio_service.dart';
import '../controllers/background_audio_controller.dart';

/// ويدجت مشغل الصوت المحسن للتشغيل في الخلفية
class BackgroundAudioPlayerWidget extends StatelessWidget {
  const BackgroundAudioPlayerWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BackgroundAudioController>(
      init: BackgroundAudioController(),
      builder: (controller) {
        return Obx(() {
          if (!controller.isPlayerVisible.value) {
            return const SizedBox.shrink();
          }

          return Container(
            height: controller.isFullScreen.value ? 
                MediaQuery.of(context).size.height * 0.9 : 80,
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: controller.isFullScreen.value
                ? _buildFullScreenPlayer(controller, context)
                : _buildMiniPlayer(controller, context),
          );
        });
      },
    );
  }

  /// مشغل مصغر
  Widget _buildMiniPlayer(BackgroundAudioController controller, BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // صورة الألبوم
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).primaryColor.withOpacity(0.1),
            ),
            child: Icon(
              Icons.music_note,
              color: Theme.of(context).primaryColor,
              size: 30,
            ),
          ),
          const SizedBox(width: 12),
          
          // معلومات المسار
          Expanded(
            child: GestureDetector(
              onTap: () => controller.togglePlayerSize(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    controller.currentTitle,
                    style: Theme.of(context).textTheme.titleMedium,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  Text(
                    controller.currentArtist,
                    style: Theme.of(context).textTheme.bodySmall,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ),
          
          // أزرار التحكم
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                onPressed: controller.playPrevious,
                icon: const Icon(Icons.skip_previous),
              ),
              Obx(() => IconButton(
                onPressed: controller.togglePlayPause,
                icon: Icon(
                  controller.isPlaying.value ? Icons.pause : Icons.play_arrow,
                  size: 32,
                ),
              )),
              IconButton(
                onPressed: controller.playNext,
                icon: const Icon(Icons.skip_next),
              ),
              IconButton(
                onPressed: controller.hidePlayer,
                icon: const Icon(Icons.close),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// مشغل كامل الشاشة
  Widget _buildFullScreenPlayer(BackgroundAudioController controller, BuildContext context) {
    return Column(
      children: [
        // شريط علوي
        AppBar(
          title: const Text('مشغل الصوت'),
          leading: IconButton(
            onPressed: () => controller.togglePlayerSize(),
            icon: const Icon(Icons.keyboard_arrow_down),
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
        ),
        
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                // صورة الألبوم الكبيرة
                Expanded(
                  flex: 3,
                  child: Container(
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                    ),
                    child: Icon(
                      Icons.music_note,
                      color: Theme.of(context).primaryColor,
                      size: 120,
                    ),
                  ),
                ),
                
                const SizedBox(height: 32),
                
                // معلومات المسار
                Text(
                  controller.currentTitle,
                  style: Theme.of(context).textTheme.headlineSmall,
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 8),
                Text(
                  controller.currentArtist,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 32),
                
                // شريط التقدم
                Obx(() => Column(
                  children: [
                    Slider(
                      value: controller.currentPosition.value.inSeconds.toDouble(),
                      max: controller.totalDuration.value.inSeconds.toDouble(),
                      onChanged: (value) {
                        controller.seekTo(Duration(seconds: value.toInt()));
                      },
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(controller.formattedPosition),
                          Text(controller.formattedDuration),
                        ],
                      ),
                    ),
                  ],
                )),
                
                const SizedBox(height: 32),
                
                // أزرار التحكم الرئيسية
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Obx(() => IconButton(
                      onPressed: controller.toggleShuffle,
                      icon: Icon(
                        Icons.shuffle,
                        color: controller.isShuffleEnabled.value
                            ? Theme.of(context).primaryColor
                            : null,
                      ),
                    )),
                    IconButton(
                      onPressed: controller.playPrevious,
                      icon: const Icon(Icons.skip_previous, size: 40),
                    ),
                    Obx(() => Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Theme.of(context).primaryColor,
                      ),
                      child: IconButton(
                        onPressed: controller.togglePlayPause,
                        icon: Icon(
                          controller.isPlaying.value ? Icons.pause : Icons.play_arrow,
                          color: Colors.white,
                          size: 40,
                        ),
                      ),
                    )),
                    IconButton(
                      onPressed: controller.playNext,
                      icon: const Icon(Icons.skip_next, size: 40),
                    ),
                    Obx(() => IconButton(
                      onPressed: controller.toggleRepeatMode,
                      icon: Icon(
                        controller.repeatMode.value == AudioServiceRepeatMode.one
                            ? Icons.repeat_one
                            : Icons.repeat,
                        color: controller.repeatMode.value != AudioServiceRepeatMode.none
                            ? Theme.of(context).primaryColor
                            : null,
                      ),
                    )),
                  ],
                ),
                
                const SizedBox(height: 16),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
