# دليل تشغيل الصوت في الخلفية

## نظرة عامة

تم تطوير نظام متكامل لتشغيل الصوت في الخلفية باستخدام المكتبات التالية:

- **`just_audio`** - للتحكم في تشغيل الصوت
- **`audio_service`** - لتشغيل الصوت في الخلفية وإظهار إشعارات التحكم
- **`audio_session`** - لإدارة جلسة الصوت والتفاعل مع النظام

## الملفات الجديدة

### 1. `lib/services/background_audio_service.dart`
خدمة أساسية تدير تشغيل الصوت في الخلفية وتتعامل مع:
- تشغيل الملفات الصوتية
- إدارة قوائم التشغيل
- التحكم في التكرار والخلط
- إظهار إشعارات التحكم

### 2. `lib/controllers/background_audio_controller.dart`
كنترولر GetX يوفر واجهة سهلة للتفاعل مع الخدمة:
- إدارة حالة واجهة المستخدم
- ربط الأحداث مع الواجهة
- توفير دوال للتحكم في التشغيل

### 3. `lib/widgets/background_audio_player_widget.dart`
ويدجت جاهز للاستخدام يعرض:
- مشغل مصغر في أسفل الشاشة
- مشغل كامل الشاشة مع جميع الخيارات
- أزرار التحكم والمعلومات

## كيفية الاستخدام

### 1. تشغيل ملف صوتي واحد

```dart
final audioController = BackgroundAudioController.instance;

// تشغيل ملف واحد
await audioController.playMediaItem(mediaItem);
```

### 2. تشغيل قائمة تشغيل

```dart
final audioController = BackgroundAudioController.instance;

// تشغيل قائمة مع تحديد الملف المطلوب
await audioController.playMediaItem(
  selectedItem,
  playlist: allAudioFiles,
  playlistName: 'جميع الأغاني',
);
```

### 3. التحكم في التشغيل

```dart
// تشغيل/إيقاف مؤقت
await audioController.togglePlayPause();

// التالي/السابق
await audioController.playNext();
await audioController.playPrevious();

// الانتقال لوقت معين
await audioController.seekTo(Duration(seconds: 30));

// تبديل التكرار
await audioController.toggleRepeatMode();

// تبديل الخلط
await audioController.toggleShuffle();
```

### 4. إدارة واجهة المشغل

```dart
// إظهار المشغل
audioController.showPlayer();

// إظهار المشغل كامل الشاشة
audioController.showPlayer(fullScreen: true);

// إخفاء المشغل
audioController.hidePlayer();

// التبديل بين المصغر والكامل
audioController.togglePlayerSize();
```

### 5. إضافة الويدجت للصفحة

```dart
class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: YourContent(),
      bottomSheet: const BackgroundAudioPlayerWidget(),
    );
  }
}
```

## المميزات

### ✅ التشغيل في الخلفية
- يستمر التشغيل حتى لو تم إغلاق التطبيق
- إشعارات تحكم في شريط الإشعارات
- دعم أزرار سماعات الرأس

### ✅ إدارة قوائم التشغيل
- دعم قوائم تشغيل متعددة
- ترتيب وخلط الأغاني
- تكرار المسار أو القائمة

### ✅ واجهة مستخدم متقدمة
- مشغل مصغر دائم
- مشغل كامل الشاشة
- شريط تقدم تفاعلي
- معلومات المسار والفنان

### ✅ تحكم متقدم
- تغيير سرعة التشغيل
- الانتقال لأي وقت
- دعم جميع صيغ الصوت

## الإعدادات المطلوبة

### Android Manifest
تم إضافة الصلاحيات والخدمات المطلوبة في `android/app/src/main/AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.FOREGROUND_SERVICE"/>
<uses-permission android:name="android.permission.FOREGROUND_SERVICE_MEDIA_PLAYBACK"/>
<uses-permission android:name="android.permission.WAKE_LOCK"/>

<service android:name="com.ryanheise.audioservice.AudioService"
    android:foregroundServiceType="mediaPlayback"
    android:exported="true">
    <intent-filter>
        <action android:name="android.media.browse.MediaBrowserService" />
    </intent-filter>
</service>
```

### أيقونة الإشعار
تم إنشاء أيقونة الإشعار في `android/app/src/main/res/drawable/ic_notification.xml`

## استبدال النظام القديم

لاستبدال `UnifiedAudioPlayerService` بالنظام الجديد:

1. استبدل `UnifiedAudioPlayerService.instance` بـ `BackgroundAudioController.instance`
2. استبدل `UnifiedAudioPlayerWidget` بـ `BackgroundAudioPlayerWidget`
3. تأكد من تسجيل `BackgroundAudioController` في `main.dart`

## نصائح للاستخدام

1. **الأداء**: الخدمة محسنة للأداء ولا تستهلك موارد إضافية
2. **البطارية**: يتم إيقاف الخدمة تلقائياً عند عدم الاستخدام
3. **الذاكرة**: إدارة ذكية للذاكرة مع تنظيف الموارد
4. **التوافق**: يعمل مع جميع إصدارات Android الحديثة

## استكشاف الأخطاء

### مشكلة: الخدمة لا تبدأ
- تأكد من تسجيل `BackgroundAudioController` في `main.dart`
- تحقق من صلاحيات Android

### مشكلة: لا تظهر إشعارات التحكم
- تأكد من وجود أيقونة الإشعار
- تحقق من إعدادات الإشعارات في النظام

### مشكلة: التشغيل يتوقف في الخلفية
- تأكد من إعدادات توفير البطارية
- تحقق من صلاحية `FOREGROUND_SERVICE`
