import 'package:get/get.dart';
import 'package:audio_service/audio_service.dart';
import '../services/background_audio_service.dart';
import '../model/media_item.dart' as local;

/// كنترولر للتحكم في تشغيل الصوت في الخلفية
class BackgroundAudioController extends GetxController {
  static BackgroundAudioController get instance =>
      Get.find<BackgroundAudioController>();

  late BackgroundAudioService _audioService;

  // حالة التشغيل
  var isPlaying = false.obs;
  var isLoading = false.obs;
  var currentPosition = Duration.zero.obs;
  var totalDuration = Duration.zero.obs;
  var playbackSpeed = 1.0.obs;

  // حالة واجهة المشغل
  var isPlayerVisible = false.obs;
  var isMinimized = true.obs;
  var isFullScreen = false.obs;

  // قائمة التشغيل والفهرس الحالي
  var currentPlaylist = <local.MediaItem>[].obs;
  var currentIndex = 0.obs;
  var currentMediaItem = Rxn<local.MediaItem>();

  // إعدادات التشغيل
  var repeatMode = AudioServiceRepeatMode.none.obs;
  var isShuffleEnabled = false.obs;

  // إعدادات الفرز والفلترة
  var sortOrder = SortOrder.name.obs;
  var isAscending = true.obs;
  var currentPlaylistName = 'جميع الأغاني'.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeAudioService();
  }

  /// تهيئة خدمة الصوت
  Future<void> _initializeAudioService() async {
    try {
      _audioService = await AudioService.init(
        builder: () => BackgroundAudioService.instance,
        config: AudioServiceConfig(
          androidNotificationChannelId: 'com.example.social_media_app.audio',
          androidNotificationChannelName: 'مشغل الصوت',
          androidNotificationChannelDescription: 'تشغيل الصوت في الخلفية',
          androidNotificationOngoing: true,
          androidShowNotificationBadge: true,
          androidNotificationIcon: 'drawable/ic_notification',
          androidStopForegroundOnPause: false,
        ),
      );

      await _audioService.initialize();
      _setupListeners();
    } catch (e) {
      print('خطأ في تهيئة خدمة الصوت: $e');
    }
  }

  /// إعداد المستمعين للأحداث
  void _setupListeners() {
    // مراقبة حالة التشغيل
    AudioService.playbackStateStream.listen((playbackState) {
      isPlaying.value = playbackState.playing;
      isLoading.value =
          playbackState.processingState == AudioProcessingState.loading;
      currentPosition.value = playbackState.updatePosition;
    });

    // مراقبة معلومات الوسائط
    AudioService.currentMediaItemStream.listen((mediaItem) {
      if (mediaItem != null) {
        // البحث عن العنصر المطابق في القائمة المحلية
        final localItem = currentPlaylist.firstWhereOrNull(
          (item) => item.id == mediaItem.id,
        );
        currentMediaItem.value = localItem;
      }
    });

    // مراقبة قائمة الانتظار
    AudioService.queueStream.listen((queue) {
      // تحديث الفهرس الحالي
      final currentItem = AudioService.currentMediaItemStream.value;
      if (currentItem != null) {
        currentIndex.value =
            queue!.indexWhere((item) => item.id == currentItem.id);
      }
    });

    // تحديث الموضع كل ثانية
    Stream.periodic(const Duration(seconds: 1)).listen((_) {
      if (isPlaying.value) {
        currentPosition.value = _audioService.currentPosition;
        totalDuration.value = _audioService.totalDuration;
      }
    });
  }

  /// تشغيل ملف صوتي مع قائمة تشغيل
  Future<void> playMediaItem(local.MediaItem item,
      {List<local.MediaItem>? playlist, String? playlistName}) async {
    try {
      isLoading.value = true;

      if (playlist != null) {
        currentPlaylist.assignAll(playlist);
        currentIndex.value =
            playlist.indexWhere((media) => media.id == item.id);
        if (currentIndex.value == -1) currentIndex.value = 0;

        await _audioService.playPlaylist(playlist,
            startIndex: currentIndex.value);
      } else {
        await _audioService.playLocalMediaItem(item);
        currentPlaylist.assignAll([item]);
        currentIndex.value = 0;
      }

      if (playlistName != null) currentPlaylistName.value = playlistName;
      currentMediaItem.value = item;
      showPlayer();
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تشغيل الملف: ${item.title}');
      print('خطأ في تشغيل الملف: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// التبديل بين تشغيل/إيقاف مؤقت
  Future<void> togglePlayPause() async {
    if (isPlaying.value) {
      await AudioService.pause();
    } else {
      await AudioService.play();
    }
  }

  /// تشغيل الملف التالي
  Future<void> playNext() async {
    await AudioService.skipToNext();
  }

  /// تشغيل الملف السابق
  Future<void> playPrevious() async {
    await AudioService.skipToPrevious();
  }

  /// الانتقال إلى وقت معين
  Future<void> seekTo(Duration position) async {
    await _audioService.seek(position);
  }

  /// تغيير سرعة التشغيل
  Future<void> setPlaybackSpeed(double speed) async {
    playbackSpeed.value = speed;
    // يمكن إضافة دعم تغيير السرعة في الخدمة لاحقاً
  }

  /// تبديل وضع التكرار
  Future<void> toggleRepeatMode() async {
    AudioServiceRepeatMode newMode;
    switch (repeatMode.value) {
      case AudioServiceRepeatMode.none:
        newMode = AudioServiceRepeatMode.one;
        break;
      case AudioServiceRepeatMode.one:
        newMode = AudioServiceRepeatMode.all;
        break;
      case AudioServiceRepeatMode.all:
        newMode = AudioServiceRepeatMode.none;
        break;
      default:
        newMode = AudioServiceRepeatMode.none;
    }

    repeatMode.value = newMode;
    await AudioService.setRepeatMode(newMode);
  }

  /// تبديل وضع الخلط
  Future<void> toggleShuffle() async {
    isShuffleEnabled.value = !isShuffleEnabled.value;
    await AudioService.setShuffleMode(isShuffleEnabled.value
        ? AudioServiceShuffleMode.all
        : AudioServiceShuffleMode.none);
  }

  /// إظهار واجهة المشغل
  void showPlayer({bool fullScreen = false}) {
    isPlayerVisible.value = true;
    isFullScreen.value = fullScreen;
    isMinimized.value = !fullScreen;
  }

  /// إخفاء واجهة المشغل
  void hidePlayer() {
    isPlayerVisible.value = false;
    isMinimized.value = true;
    isFullScreen.value = false;
  }

  /// التبديل بين واجهة كاملة ومصغرة
  void togglePlayerSize() {
    if (isFullScreen.value) {
      isFullScreen.value = false;
      isMinimized.value = true;
    } else {
      isFullScreen.value = true;
      isMinimized.value = false;
    }
  }

  /// إيقاف التشغيل وإغلاق الخدمة
  Future<void> stop() async {
    await AudioService.stop();
    hidePlayer();
  }

  @override
  void onClose() {
    _audioService.dispose();
    super.onClose();
  }

  // Getters للمعلومات الحالية
  String get currentTitle => currentMediaItem.value?.title ?? '';
  String get currentArtist => currentMediaItem.value?.artist ?? 'مجهول';
  String get currentAlbum => currentMediaItem.value?.album ?? 'مجهول';
  bool get hasCurrentTrack => currentMediaItem.value != null;
  String get formattedPosition => _formatDuration(currentPosition.value);
  String get formattedDuration => _formatDuration(totalDuration.value);

  /// تنسيق الوقت إلى صيغة 00:00
  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}

/// أنواع الترتيب لقائمة التشغيل
enum SortOrder { name, artist, album, dateAdded, duration }
