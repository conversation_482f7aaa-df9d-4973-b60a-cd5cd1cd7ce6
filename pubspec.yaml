name: social_media_app
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.6.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.8
  get: ^4.7.2
  file_picker: ^8.3.0
  just_audio: ^0.10.3
  video_player: ^2.9.5
  photo_manager: ^3.7.1
  permission_handler: ^12.0.0+1
  hive_flutter: ^1.1.0
  hive: ^2.2.3
  path_provider: ^2.1.5
  device_info_plus: ^10.1.0
  share_plus: ^7.2.1
  url_launcher: ^6.2.1
  package_info_plus: 8.3.0
  keep_screen_on: ^4.0.0
  metadata_god: ^1.0.0
  system_theme: ^3.1.2
  animated_background: ^2.0.0
  gradient_borders: ^1.0.1
  shared_preferences: ^2.2.2
  dart_tags: ^0.4.1
  wakelock_plus: ^1.3.2
  marquee: ^2.3.0
  google_fonts: ^6.2.1
  on_audio_query: ^2.9.0  # مؤقتاً معطل بسبب مشكلة namespace
  sliding_up_panel: ^2.0.0+1
  awesome_notifications: ^0.10.1
  lazy_load_scrollview: ^1.3.0
  audio_service: ^0.18.15
  just_audio_background: ^0.0.1-beta.13
  audio_session: ^0.1.21
#  on_audio_query_android_v1_8: ^1.1.1
#  audiometry: ^1.1.1




dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.15
  hive_generator: ^2.0.1
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
