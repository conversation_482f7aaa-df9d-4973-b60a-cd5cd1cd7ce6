import 'dart:async';
import 'package:audio_service/audio_service.dart';
import 'package:audio_session/audio_session.dart';
import 'package:just_audio/just_audio.dart';
import 'package:get/get.dart';
import '../model/media_item.dart' as local;

/// خدمة تشغيل الصوت في الخلفية باستخدام audio_service
class BackgroundAudioService extends BaseAudioHandler
    with <PERSON><PERSON><PERSON><PERSON><PERSON>, SeekHandler {
  static BackgroundAudioService? _instance;
  static BackgroundAudioService get instance =>
      _instance ??= BackgroundAudioService._();

  BackgroundAudioService._();

  final AudioPlayer _audioPlayer = AudioPlayer();
  final Completer<void> _completer = Completer<void>();

  // متغيرات التحكم
  var currentPlaylist = <local.MediaItem>[].obs;
  var currentIndex = 0.obs;
  var repeatMode = AudioServiceRepeatMode.none.obs;
  var isShuffleEnabled = false.obs;
  var shuffledIndices = <int>[].obs;

  /// تهيئة الخدمة
  Future<void> initialize() async {
    // تهيئة جلسة الصوت
    final session = await AudioSession.instance;
    await session.configure(const AudioSessionConfiguration.music());

    // ربط الأحداث
    _audioPlayer.playbackEventStream.map(_transformEvent).pipe(playbackState);
    _audioPlayer.sequenceStateStream.listen(_updateMediaItem);
    _audioPlayer.playerStateStream.listen(_playerStateListener);

    // إعداد معالجة الأخطاء
    _audioPlayer
        .setAudioSource(AudioSource.uri(Uri.parse("silence")))
        .catchError((error) => null);

    _completer.complete();
  }

  /// تحويل أحداث المشغل إلى حالة التشغيل
  PlaybackState _transformEvent(PlaybackEvent event) {
    return PlaybackState(
      controls: [
        MediaControl.skipToPrevious,
        if (_audioPlayer.playing) MediaControl.pause else MediaControl.play,
        MediaControl.skipToNext,
        MediaControl.stop,
      ],
      systemActions: const {
        MediaAction.seek,
        MediaAction.seekForward,
        MediaAction.seekBackward,
      },
      androidCompactActionIndices: const [0, 1, 2],
      processingState: const {
        ProcessingState.idle: AudioProcessingState.idle,
        ProcessingState.loading: AudioProcessingState.loading,
        ProcessingState.buffering: AudioProcessingState.buffering,
        ProcessingState.ready: AudioProcessingState.ready,
        ProcessingState.completed: AudioProcessingState.completed,
      }[_audioPlayer.processingState]!,
      playing: _audioPlayer.playing,
      updatePosition: _audioPlayer.position,
      bufferedPosition: _audioPlayer.bufferedPosition,
      speed: _audioPlayer.speed,
      queueIndex: _audioPlayer.currentIndex,
    );
  }

  /// تحديث معلومات الوسائط الحالية
  void _updateMediaItem(SequenceState? sequenceState) {
    final item = sequenceState?.currentSource?.tag as MediaItem?;
    if (item != null) {
      mediaItem.add(item);
    }
  }

  /// مراقب حالة المشغل
  void _playerStateListener(PlayerState state) {
    if (state.processingState == ProcessingState.completed) {
      _handleTrackCompleted();
    }
  }

  /// معالجة انتهاء المسار
  void _handleTrackCompleted() {
    switch (repeatMode.value) {
      case AudioServiceRepeatMode.one:
        _audioPlayer.seek(Duration.zero);
        _audioPlayer.play();
        break;
      case AudioServiceRepeatMode.all:
        skipToNext();
        break;
      case AudioServiceRepeatMode.none:
        if (currentIndex.value < currentPlaylist.length - 1) {
          skipToNext();
        } else {
          stop();
        }
        break;
      default:
        break;
    }
  }

  /// تشغيل قائمة تشغيل
  Future<void> playPlaylist(List<local.MediaItem> playlist,
      {int startIndex = 0}) async {
    await _completer.future;

    currentPlaylist.assignAll(playlist);
    currentIndex.value = startIndex;

    if (isShuffleEnabled.value) {
      _generateShuffledIndices();
    }

    // تحويل قائمة التشغيل إلى AudioSource
    final audioSources = playlist
        .map((item) => AudioSource.uri(
              Uri.file(item.path),
              tag: MediaItem(
                id: item.id ?? '',
                album: item.album ?? 'مجهول',
                title: item.title ?? 'مجهول',
                artist: item.artist ?? 'مجهول',
                duration: item.duration,
                artUri: item.displayAlbum != null
                    ? Uri.parse(item.displayAlbum!)
                    : null,
              ),
            ))
        .toList();

    final concatenatingAudioSource =
        ConcatenatingAudioSource(children: audioSources);

    await _audioPlayer.setAudioSource(concatenatingAudioSource,
        initialIndex: startIndex);
    await _audioPlayer.play();

    // تحديث قائمة الانتظار
    queue.add(audioSources.map((source) => source.tag as MediaItem).toList());
  }

  /// تشغيل مسار واحد
  Future<void> playLocalMediaItem(local.MediaItem item) async {
    await playPlaylist([item], startIndex: 0);
  }

  @override
  Future<void> playMediaItem(MediaItem mediaItem) async {
    // تحويل MediaItem إلى local.MediaItem إذا لزم الأمر
    // هذا للتوافق مع audio_service
    await _audioPlayer.play();
  }

  @override
  Future<void> play() async {
    await _audioPlayer.play();
  }

  @override
  Future<void> pause() async {
    await _audioPlayer.pause();
  }

  @override
  Future<void> stop() async {
    await _audioPlayer.stop();
    await super.stop();
  }

  @override
  Future<void> seek(Duration position) async {
    await _audioPlayer.seek(position);
  }

  @override
  Future<void> skipToNext() async {
    if (currentPlaylist.isEmpty) return;

    int nextIndex;
    if (isShuffleEnabled.value) {
      nextIndex = _getNextShuffledIndex();
    } else {
      nextIndex = (currentIndex.value + 1) % currentPlaylist.length;
    }

    currentIndex.value = nextIndex;
    await _audioPlayer.seekToNext();
  }

  @override
  Future<void> skipToPrevious() async {
    if (currentPlaylist.isEmpty) return;

    int prevIndex;
    if (isShuffleEnabled.value) {
      prevIndex = _getPreviousShuffledIndex();
    } else {
      prevIndex = currentIndex.value - 1;
      if (prevIndex < 0) prevIndex = currentPlaylist.length - 1;
    }

    currentIndex.value = prevIndex;
    await _audioPlayer.seekToPrevious();
  }

  @override
  Future<void> setRepeatMode(AudioServiceRepeatMode repeatMode) async {
    this.repeatMode.value = repeatMode;

    // تحويل إلى LoopMode الخاص بـ just_audio
    switch (repeatMode) {
      case AudioServiceRepeatMode.none:
        await _audioPlayer.setLoopMode(LoopMode.off);
        break;
      case AudioServiceRepeatMode.one:
        await _audioPlayer.setLoopMode(LoopMode.one);
        break;
      case AudioServiceRepeatMode.all:
        await _audioPlayer.setLoopMode(LoopMode.all);
        break;
      default:
        await _audioPlayer.setLoopMode(LoopMode.off);
    }
  }

  @override
  Future<void> setShuffleMode(AudioServiceShuffleMode shuffleMode) async {
    isShuffleEnabled.value = shuffleMode == AudioServiceShuffleMode.all;

    if (isShuffleEnabled.value) {
      _generateShuffledIndices();
      await _audioPlayer.setShuffleModeEnabled(true);
    } else {
      await _audioPlayer.setShuffleModeEnabled(false);
    }
  }

  /// توليد قائمة الخلط
  void _generateShuffledIndices() {
    shuffledIndices.clear();
    shuffledIndices
        .addAll(List.generate(currentPlaylist.length, (index) => index));
    shuffledIndices.shuffle();
  }

  /// الحصول على الفهرس التالي في وضع Shuffle
  int _getNextShuffledIndex() {
    int currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    return shuffledIndices[(currentShuffledIndex + 1) % shuffledIndices.length];
  }

  /// الحصول على الفهرس السابق في وضع Shuffle
  int _getPreviousShuffledIndex() {
    int currentShuffledIndex = shuffledIndices.indexOf(currentIndex.value);
    int prevIndex = currentShuffledIndex - 1;
    if (prevIndex < 0) prevIndex = shuffledIndices.length - 1;
    return shuffledIndices[prevIndex];
  }

  /// تنظيف الموارد
  @override
  Future<void> onTaskRemoved() async {
    await stop();
  }

  /// إغلاق الخدمة
  Future<void> dispose() async {
    await _audioPlayer.dispose();
  }

  // Getters للمعلومات الحالية
  bool get isPlaying => _audioPlayer.playing;
  Duration get currentPosition => _audioPlayer.position;
  Duration get totalDuration => _audioPlayer.duration ?? Duration.zero;
  local.MediaItem? get currentMediaItem =>
      currentPlaylist.isNotEmpty ? currentPlaylist[currentIndex.value] : null;
}
