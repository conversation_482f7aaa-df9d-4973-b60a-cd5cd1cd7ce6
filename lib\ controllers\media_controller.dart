import 'dart:io';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
// import 'package:on_audio_query/on_audio_query.dart'; // معطل بسبب مشكلة namespace
import 'package:photo_manager/photo_manager.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../model/media_item.dart';
import '../model/folder_model.dart';

class MediaController extends GetxController {
  // final OnAudioQuery _audioQuery = OnAudioQuery(); // معطل بسبب مشكلة namespace

  // === متغيرات الصفحة الأولى ===
  var allAudioFiles = <MediaItem>[].obs;
  var allVideoFiles = <MediaItem>[].obs;
  var recentAudio = <MediaItem>[].obs;
  var recentVideo = <MediaItem>[].obs;
  var favoriteAudio = <MediaItem>[].obs;
  var favoriteVideo = <MediaItem>[].obs;
  var isLoadingAudio = false.obs;
  var isLoadingVideo = false.obs;
  var hasPermission = false.obs;

  // === متغيرات الصفحة الثانية === (احتفظنا بنفس الاسم)
  final RxList<AssetEntity> videos = <AssetEntity>[].obs;
  final RxList<AssetEntity> audios = <AssetEntity>[].obs;
  final RxList<String> artists = <String>[].obs;
  final RxList<String> albums = <String>[].obs;

  // مجلدات
  final RxList<FolderModel> videoFolders = <FolderModel>[].obs;
  final RxList<FolderModel> audioFolders = <FolderModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    requestPermissions();
  }

  Future<void> requestPermissions() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      if (androidInfo.version.sdkInt >= 33) {
        await [
          Permission.audio,
          Permission.videos,
          Permission.photos,
        ].request();
      } else {
        await [
          Permission.storage,
          Permission.manageExternalStorage,
        ].request();
      }
    }

    final result = await PhotoManager.requestPermissionExtend();
    hasPermission.value = result.isAuth;

    if (hasPermission.value) {
      await loadAllMedia();
    }
  }

  Future<void> loadAllMedia() async {
    await Future.wait([
      loadAudioFiles(),
      loadVideoFiles(),
      loadArtistsAndAlbums(),
    ]);
  }

  Future<void> loadAudioFiles() async {
    try {
      isLoadingAudio.value = true;

      final result = await PhotoManager.requestPermissionExtend();
      if (!result.isAuth) return;

      final albumsList = await PhotoManager.getAssetPathList(
        type: RequestType.audio,
        onlyAll: true,
      );

      List<MediaItem> audioItems = [];
      List<AssetEntity> audioAssets = [];

      for (var album in albumsList) {
        final assets = await album.getAssetListPaged(
          page: 0,
          size: await album.assetCountAsync,
        );

        audioAssets.addAll(assets);

        for (var asset in assets) {
          final file = await asset.file;
          if (file != null) {
            audioItems.add(MediaItem(
              id: asset.id,
              title: asset.title ?? file.path.split('/').last,
              path: file.path,
              duration: Duration(seconds: asset.duration),
              type: MediaType.audio,
              size: await file.length(),
              dateAdded: asset.createDateTime,
            ));
          }
        }
      }

      allAudioFiles.assignAll(audioItems);
      audios.assignAll(audioAssets);
      _groupAudioByFolders(audioItems);
    } catch (e) {
      print("خطأ في تحميل الصوتيات: $e");
    } finally {
      isLoadingAudio.value = false;
    }
  }

  Future<void> loadVideoFiles() async {
    try {
      isLoadingVideo.value = true;

      final result = await PhotoManager.requestPermissionExtend();
      if (!result.isAuth) return;

      final albumsList = await PhotoManager.getAssetPathList(
        type: RequestType.video,
        onlyAll: true,
      );

      List<MediaItem> videoItems = [];
      List<AssetEntity> videoAssets = [];

      for (var album in albumsList) {
        final assets = await album.getAssetListPaged(
          page: 0,
          size: await album.assetCountAsync,
        );

        videoAssets.addAll(assets);

        for (var asset in assets) {
          final file = await asset.file;
          if (file != null) {
            videoItems.add(MediaItem(
              id: asset.id,
              title: asset.title ?? file.path.split('/').last,
              path: file.path,
              duration: Duration(seconds: asset.duration),
              type: MediaType.video,
              size: await file.length(),
              dateAdded: asset.createDateTime,
            ));
          }
        }
      }

      allVideoFiles.value = videoItems;
      videos.assignAll(videoAssets);
      _groupVideoByFolders(videoItems);
    } catch (e) {
      print('خطأ في تحميل الفيديو: $e');
    } finally {
      isLoadingVideo.value = false;
    }
  }

  Future<void> loadArtistsAndAlbums() async {
    try {
      // استخراج الفنانين والألبومات من الملفات الصوتية المحملة
      Set<String> artistSet = {};
      Set<String> albumSet = {};

      for (var item in allAudioFiles) {
        if (item.artist != null && item.artist!.isNotEmpty) {
          artistSet.add(item.artist!);
        }
        if (item.album != null && item.album!.isNotEmpty) {
          albumSet.add(item.album!);
        }
      }

      artists.value = artistSet.toList()..sort();
      albums.value = albumSet.toList()..sort();
    } catch (e) {
      print('خطأ في تحميل الفنانين/الألبومات: $e');
    }
  }

  void _groupAudioByFolders(List<MediaItem> items) {
    final folderMap = <String, List<MediaItem>>{};

    for (var item in items) {
      final folderPath = File(item.path).parent.path;
      folderMap.putIfAbsent(folderPath, () => []).add(item);
    }

    audioFolders.value = folderMap.entries
        .map((e) => FolderModel(
              name: e.key.split('/').last,
              path: e.key,
              items: e.value,
              type: MediaType.audio,
            ))
        .toList();
  }

  void _groupVideoByFolders(List<MediaItem> items) {
    final folderMap = <String, List<MediaItem>>{};

    for (var item in items) {
      final folderPath = File(item.path).parent.path;
      folderMap.putIfAbsent(folderPath, () => []).add(item);
    }

    videoFolders.value = folderMap.entries
        .map((e) => FolderModel(
              name: e.key.split('/').last,
              path: e.key,
              items: e.value,
              type: MediaType.video,
            ))
        .toList();
  }

  void addToFavorites(MediaItem item) {
    if (item.type == MediaType.audio) {
      if (!favoriteAudio.any((e) => e.id == item.id)) {
        favoriteAudio.add(item);
      }
    } else {
      if (!favoriteVideo.any((e) => e.id == item.id)) {
        favoriteVideo.add(item);
      }
    }
  }

  void removeFromFavorites(MediaItem item) {
    if (item.type == MediaType.audio) {
      favoriteAudio.removeWhere((e) => e.id == item.id);
    } else {
      favoriteVideo.removeWhere((e) => e.id == item.id);
    }
  }

  void addToRecent(MediaItem item) {
    if (item.type == MediaType.audio) {
      recentAudio.removeWhere((e) => e.id == item.id);
      recentAudio.insert(0, item);
      if (recentAudio.length > 50) recentAudio.removeLast();
    } else {
      recentVideo.removeWhere((e) => e.id == item.id);
      recentVideo.insert(0, item);
      if (recentVideo.length > 50) recentVideo.removeLast();
    }
  }
}
